"""
代理池管理模块 - 简化版
提供简洁、高效的代理管理功能
"""
import asyncio
import random
import string
import time
import logging
from typing import List, Optional, Tuple, Dict
from dataclasses import dataclass

import rnet
from rnet import Impersonate

from config import config
from user_agents import UserAgents
from header_utils import get_default_headers


logger = logging.getLogger(__name__)


@dataclass
class Proxy:
    """代理信息类"""
    url: str
    user_agent: str
    language: str = "US"
    created_at: float = None
    cookies: List[Tuple[str, str]] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()
        if self.cookies is None:
            self.cookies = []

    def is_expired(self, max_age: int = 300) -> bool:
        """检查代理是否过期(默认5分钟)"""
        return time.time() - self.created_at > max_age

    def get_headers(self) -> Dict[str, str]:
        """获取代理的HTTP头部"""
        return get_default_headers(self.user_agent, self.language)

    def set_cookies(self, cookies: List[Tuple[str, str]]):
        """设置 cookies"""
        self.cookies = cookies
        logger.debug(f"代理 {self.url} 设置了 {len(cookies)} 个 cookies")

    def get_cookies(self) -> List[Tuple[str, str]]:
        """获取 cookies"""
        return self.cookies or []


class ProxyGenerator:
    """代理生成器"""

    def __init__(self, user_agents: UserAgents):
        self.user_agents = user_agents

    def generate_proxy_url(self) -> Optional[str]:
        """生成代理URL"""
        countries = config.proxy_countries or ["US", "GB", "CA", "AU", "DE"]
        template = config.proxy_url_template

        if not template:
            return None

        # 生成随机会话ID和国家
        session = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
        country = random.choice(countries)

        # 替换模板中的占位符
        return template.replace("{}", country, 1).replace("{}", session, 1)

    def create_proxy(self) -> Optional[Proxy]:
        """创建一个新的代理对象"""
        proxy_url = self.generate_proxy_url()
        if not proxy_url:
            return None

        user_agent = self.user_agents.random()

        return Proxy(
            url=proxy_url,
            user_agent=user_agent,
            language="US"
        )


class ProxyTester:
    """代理测试器"""

    @staticmethod
    async def test_proxy(proxy: Proxy) -> bool:
        """测试代理是否可用"""
        try:
            headers = proxy.get_headers()
            # 打印请求头和代理信息
            logger.info(f"测试代理: {proxy.url}")

            # 创建 rnet 客户端
            client = rnet.Client(
                impersonate=Impersonate.Firefox136,
                timeout=10,
                cookie_store=True,
                allow_redirects=False
            )

            # 测试连接
            resp = await client.get(
                "https://auth.openai.com",
                proxy=proxy.url,
                headers=headers
            )
            # 打印状态
            logger.info(resp.status)

            # 检查响应时间和状态
            if resp.status in [200, 302]:
                # 提取并保存 cookies
                try:
                    cookies: List[Tuple[str, str]] = [
                        (c.name(), c.value()) for c in resp.cookies()
                    ]
                    proxy.set_cookies(cookies)
                except Exception as cookie_error:
                    logger.debug(f"提取 cookies 失败: {cookie_error}")

                return True

            return False

        except Exception as e:
            logger.info(f"代理测试失败 {proxy.url}: {e}")
            return False



class ProxyPool:
    """简化的代理池管理类"""

    def __init__(self, max_size: int, min_size: int, user_agents: UserAgents):
        self.max_size = max_size
        self.min_size = min_size
        self.generator = ProxyGenerator(user_agents)
        self.tester = ProxyTester()
        self.proxies: List[Proxy] = []
        self._lock = asyncio.Lock()
        self._auto_fill_enabled = True  # 控制是否自动填充代理

        # 启动维护任务
        asyncio.create_task(self._maintenance_task())

    async def _maintenance_task(self):
        """维护任务：清理过期代理和自动填充"""
        # 首次启动时立即执行一次维护
        try:
            await self._cleanup_expired()
            await self._auto_fill()
        except Exception as e:
            logger.error(f"初始维护任务失败: {e}")

        while True:
            try:
                await asyncio.sleep(30)  # 每30秒检查一次
                await self._cleanup_expired()
                await self._auto_fill()
            except Exception as e:
                logger.error(f"维护任务失败: {e}")

    async def _cleanup_expired(self):
        """清理过期代理"""
        async with self._lock:
            before = len(self.proxies)
            self.proxies = [p for p in self.proxies if not p.is_expired()]
            after = len(self.proxies)

            if before > after:
                logger.info(f"清理了 {before - after} 个过期代理")

    async def _auto_fill(self):
        """自动填充代理池"""
        if not self._auto_fill_enabled:
            logger.debug("自动填充已禁用，跳过代理添加")
            return

        current_size = len(self.proxies)
        if current_size < self.min_size:
            needed = self.min_size - current_size
            logger.info(f"自动填充代理池: 需要 {needed} 个")
            await self._fill_proxies(needed)

    async def get_proxy(self) -> Optional[Tuple[str, str, str, Dict[str, str]]]:
        """获取一个可用代理"""
        async with self._lock:
            if not self.proxies:
                return None

            proxy = random.choice(self.proxies)
            headers = proxy.get_headers()

            return proxy.url, proxy.language, proxy.user_agent, headers

    async def get_proxy_object(self) -> Optional[Proxy]:
        """获取一个可用代理对象"""
        async with self._lock:
            if not self.proxies:
                return None

            return random.choice(self.proxies)

    async def remove_proxy(self, proxy_url: str):
        """移除指定代理"""
        async with self._lock:
            before = len(self.proxies)
            self.proxies = [p for p in self.proxies if p.url != proxy_url]

            if len(self.proxies) < before:
                logger.debug(f"移除代理: {proxy_url}")

    async def update_proxy_cookies(self, proxy_url: str, cookies: List[Tuple[str, str]]):
        """更新指定代理的 cookies"""
        async with self._lock:
            for proxy in self.proxies:
                if proxy.url == proxy_url:
                    proxy.set_cookies(cookies)
                    break

    async def get_stats(self) -> Dict[str, int]:
        """获取池统计信息"""
        return {
            "current_size": len(self.proxies),
            "max_size": self.max_size,
            "min_size": self.min_size,
            "auto_fill_enabled": self._auto_fill_enabled
        }

    def set_auto_fill_enabled(self, enabled: bool):
        """设置是否启用自动填充"""
        self._auto_fill_enabled = enabled
        if enabled:
            logger.info("代理自动填充已启用")
        else:
            logger.info("代理自动填充已禁用")

    def is_auto_fill_enabled(self) -> bool:
        """检查是否启用自动填充"""
        return self._auto_fill_enabled

    async def _fill_proxies(self, count: int):
        """填充指定数量的代理"""
        if count <= 0:
            return

        # 并发生成和测试代理
        tasks = []
        for _ in range(count):
            task = asyncio.create_task(self._create_valid_proxy())
            tasks.append(task)

        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 添加有效的代理
        added = 0
        async with self._lock:
            for result in results:
                if isinstance(result, Proxy) and len(self.proxies) < self.max_size:
                    self.proxies.append(result)
                    added += 1

        if added > 0:
            logger.info(f"成功添加 {added} 个代理")

    async def _create_valid_proxy(self) -> Optional[Proxy]:
        """创建并验证一个代理"""
        try:
            proxy = self.generator.create_proxy()
            if proxy and await self.tester.test_proxy(proxy):
                return proxy
            return None
        except Exception as e:
            logger.debug(f"创建代理失败: {e}")
            return None

    # 兼容性方法
    async def size(self) -> int:
        """获取当前池大小"""
        return len(self.proxies)

    async def fill(self):
        """手动填充代理池"""
        current_size = len(self.proxies)
        if current_size == 0:
            # 如果代理池为空，填充到最小大小
            logger.info(f"代理池为空，开始填充到最小大小: {self.min_size}")
            await self._fill_proxies(self.min_size)
        else:
            # 否则执行正常的自动填充逻辑
            await self._auto_fill()


class SimpleProxyManager:
    """简化的代理管理器"""

    def __init__(self, proxy_pool: ProxyPool):
        self.proxy_pool = proxy_pool

    async def get_proxy_and_client(self) -> Tuple[str, str, str, rnet.Client, Dict[str, str]]:
        """获取代理和HTTP客户端"""
        # 尝试获取代理对象
        proxy = await self.proxy_pool.get_proxy_object()
        if not proxy:
            # 如果没有代理，尝试填充
            await self.proxy_pool.fill()
            proxy = await self.proxy_pool.get_proxy_object()

            if not proxy:
                raise Exception("无法获取可用代理")

        # 创建HTTP客户端
        client = rnet.Client(
            impersonate=Impersonate.Firefox136,
            timeout=30,
            cookie_store=True,
            allow_redirects=False
        )

        # 设置已保存的 cookies
        try:
            cookies = proxy.get_cookies()
            if cookies:
                from urllib.parse import urlparse
                cookies_url = "https://auth.openai.com"
                for name, value in cookies:
                    cookie_str = f"{name}={value}"
                    # 使用 rnet 的 set_cookies 方法
                    client.set_cookies(cookies_url, [cookie_str])
                logger.debug(f"为代理 {proxy.url} 设置了 {len(cookies)} 个 cookies")
        except Exception as e:
            logger.debug(f"设置 cookies 失败: {e}")

        headers = proxy.get_headers()
        return proxy.url, proxy.language, proxy.user_agent, client, headers

    async def remove_proxy(self, proxy_url: str):
        """移除代理"""
        await self.proxy_pool.remove_proxy(proxy_url)

    async def update_proxy_cookies(self, proxy_url: str, cookies: List[Tuple[str, str]]):
        """更新指定代理的 cookies"""
        await self.proxy_pool.update_proxy_cookies(proxy_url, cookies)


# 兼容性别名
Auth0ProxyManager = SimpleProxyManager
