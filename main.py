"""
主服务器文件
对应Rust项目中的main.rs
"""
import asyncio
import logging
import random
import time
from contextlib import asynccontextmanager
from typing import Dict

import uvicorn
from fastapi import FastAPI, HTTPException, Form
from fastapi.responses import JSONResponse

from config import config
from proxy_manager import ProxyPool, SimpleProxyManager
from platform_login import platform_login, PlatformLoginForm, PlatformLoginResponse, AuthApiError
from user_agents import UserAgents


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 全局变量
user_agents = None
proxy_pool = None
proxy_manager = None
request_tracker = None
proxy_pool_initialized = False
proxy_pool_initializing = False


class RequestTracker:
    """请求跟踪器"""
    
    def __init__(self):
        self.last_request_time = time.time()
        self.request_count = 0
    
    def record_request(self):
        """记录请求"""
        self.last_request_time = time.time()
        self.request_count += 1
    
    def should_run_tasks(self) -> bool:
        """判断是否应该运行后台任务"""
        # 如果最近60秒内有请求，则运行任务
        return time.time() - self.last_request_time < 60

    def get_idle_time(self) -> float:
        """获取空闲时间（秒）"""
        return time.time() - self.last_request_time


async def proxy_maintenance_task():
    """代理池维护任务 - 简化版"""
    while True:
        try:
            await asyncio.sleep(60)  # 每分钟检查一次

            if request_tracker.should_run_tasks():
                stats = await proxy_pool.get_stats()
                logger.debug(f"代理池状态: {stats}")

                # 代理池已经有自动维护功能，这里只需要监控
                if stats["current_size"] == 0:
                    logger.warning("代理池为空，尝试手动填充")
                    await proxy_pool.fill()
            else:
                # 如果60秒没有请求，停止代理添加
                idle_time = request_tracker.get_idle_time()
                logger.info(f"已空闲 {idle_time:.1f} 秒，停止代理添加")
                # 通知代理池停止自动添加
                if hasattr(proxy_pool, 'set_auto_fill_enabled'):
                    proxy_pool.set_auto_fill_enabled(False)

        except Exception as e:
            logger.error(f"代理维护任务出错: {e}")
            await asyncio.sleep(30)


async def initialize_basic_components():
    """初始化基础组件（不包括代理池填充）"""
    global user_agents, proxy_pool, proxy_manager, request_tracker

    logger.info("程序配置信息:")
    logger.info(f"- 代理池大小: {config.proxy_pool_size}")
    logger.info(f"- 代理池最小大小: {config.proxy_pool_min_size}")
    logger.info(f"- 服务器端口: {config.server_port}")
    logger.info(f"- 代理URL模板: {config.proxy_url_template}")

    # 初始化组件
    logger.info("初始化用户代理...")
    user_agents = UserAgents()

    logger.info("初始化代理池...")
    proxy_pool = ProxyPool(
        config.proxy_pool_size,
        config.proxy_pool_min_size,
        user_agents
    )
    proxy_manager = SimpleProxyManager(proxy_pool)
    request_tracker = RequestTracker()

async def initialize_proxy_pool_async():
    """异步初始化代理池"""
    global proxy_pool_initialized, proxy_pool_initializing

    if proxy_pool_initializing or proxy_pool_initialized:
        return

    proxy_pool_initializing = True

    try:
        logger.info("开始填充代理池，请稍候...")
        await proxy_pool.fill()

        # 验证代理池状态
        stats = await proxy_pool.get_stats()
        logger.info(f"代理池初始化完成: {stats}")

        if stats["current_size"] == 0:
            logger.warning("警告: 代理池为空，服务可能无法正常工作")
        else:
            logger.info(f"代理池准备就绪，包含 {stats['current_size']} 个代理")

        proxy_pool_initialized = True
    except Exception as e:
        logger.error(f"代理池初始化失败: {e}")
    finally:
        proxy_pool_initializing = False

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global user_agents, proxy_pool, proxy_manager, request_tracker

    # 如果组件还没有初始化（直接运行main.py的情况），则进行基础初始化
    if proxy_pool is None:
        await initialize_basic_components()

    # 启动后台任务
    maintenance_task = asyncio.create_task(proxy_maintenance_task())

    # 在服务启动后异步初始化代理池
    proxy_init_task = asyncio.create_task(initialize_proxy_pool_async())

    yield

    # 关闭时清理
    maintenance_task.cancel()
    proxy_init_task.cancel()

    try:
        await maintenance_task
    except asyncio.CancelledError:
        pass

    try:
        await proxy_init_task
    except asyncio.CancelledError:
        pass

    logger.info("应用关闭完成")


async def create_initialized_app() -> FastAPI:
    """创建并初始化应用"""
    # 只初始化基础组件，代理池将在服务启动后异步初始化
    await initialize_basic_components()

    # 创建FastAPI应用
    app = FastAPI(
        title="OpenAI Platform Login Service",
        description="OpenAI平台登录服务的Python实现",
        version="1.0.0",
        lifespan=lifespan
    )

    # 添加中间件和路由
    setup_middleware_and_routes(app)

    logger.info("应用创建完成，基础组件已初始化")
    return app

def setup_middleware_and_routes(app: FastAPI):
    """设置中间件和路由"""

    @app.middleware("http")
    async def request_tracking_middleware(request, call_next):
        """请求跟踪中间件"""
        if request_tracker:
            request_tracker.record_request()
            # 如果有新请求，重新启用代理自动填充
            if proxy_pool and hasattr(proxy_pool, 'set_auto_fill_enabled'):
                if not proxy_pool.is_auto_fill_enabled():
                    logger.info("检测到新请求，重新启用代理自动填充")
                    proxy_pool.set_auto_fill_enabled(True)
        response = await call_next(request)
        return response

    @app.get("/health")
    async def health_check():
        """健康检查"""
        return {
            "status": "OK",
            "proxy_pool_initialized": proxy_pool_initialized,
            "proxy_pool_initializing": proxy_pool_initializing
        }

    @app.post("/auth/platform/login", response_model=PlatformLoginResponse)
    async def platform_login_endpoint(
        username: str = Form(...),
        password: str = Form(...)
    ):
        """平台登录端点"""
        try:
            if not proxy_pool:
                raise HTTPException(status_code=503, detail="代理池未初始化")

            if not proxy_pool_initialized:
                if proxy_pool_initializing:
                    raise HTTPException(status_code=503, detail="代理池正在初始化中，请稍后重试")
                else:
                    # 如果代理池没有在初始化，尝试启动初始化
                    asyncio.create_task(initialize_proxy_pool_async())
                    raise HTTPException(status_code=503, detail="代理池正在初始化中，请稍后重试")

            form = PlatformLoginForm(username=username, password=password)
            result = await platform_login(form, proxy_pool)
            return result
        except AuthApiError as e:
            logger.error(f"认证API失败: {e.error_data}")
            # 返回包含JSON字符串的detail字段
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
#             logger.error(f"平台登录失败: {e}")

            # 根据错误类型返回不同的HTTP状态码
            error_msg = str(e)
            if any(keyword in error_msg.lower() for keyword in [
                "unexpected eof while tunneling",
                "proxy error",
                "error sending request for url",
                "429 too many requests"
            ]):
                raise HTTPException(status_code=403, detail=error_msg)
            else:
                raise HTTPException(status_code=400, detail=error_msg)

    @app.get("/proxyinfo")
    async def get_proxy_info():
        """获取代理池信息"""
        try:
            if not proxy_pool:
                raise HTTPException(status_code=503, detail="代理池未初始化")

            stats = await proxy_pool.get_stats()
            stats["proxy_pool_initialized"] = proxy_pool_initialized
            stats["proxy_pool_initializing"] = proxy_pool_initializing
            return stats
        except Exception as e:
            logger.error(f"获取代理信息失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))

# 创建FastAPI应用（用于直接运行）
app = FastAPI(
    title="OpenAI Platform Login Service",
    description="OpenAI平台登录服务的Python实现",
    version="1.0.0",
    lifespan=lifespan
)

# 为直接运行的应用设置路由
setup_middleware_and_routes(app)


if __name__ == "__main__":
    # 直接运行时使用原来的方式（带有lifespan初始化）
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=config.server_port,
        log_level="info",
        access_log=True
    )
